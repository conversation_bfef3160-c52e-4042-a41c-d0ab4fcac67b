import React from 'react';
import { Link } from 'react-router-dom';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminDepositData } from '../types';
import { formatNumber } from '@/utils/dateUtils';
import { mapTransactionType } from '@/constants/common.constant';

interface DepositTablesProps {
  data: AdminDepositData;
  areaFilters: any;
  transferDate: string;
  switchLayoutDate: string;
}

export const DepositTables: React.FC<DepositTablesProps> = ({
  data,
  areaFilters,
  transferDate,
  switchLayoutDate
}) => {
  const lastPaymentIndex = data.agxMerchantPayments.length - 1;
  const totalPayment = data.agxMerchantPayments[lastPaymentIndex];

  const getDepositDetailLink = () => {
    const area = areaFilters.areaSelected === 'all' ? '' : areaFilters.areaSelected;
    const subArea = areaFilters.subAreaSelected === 'all' ? '' : areaFilters.subAreaSelected;
    const merchant = areaFilters.merchantSelected === 'all' ? '' : areaFilters.merchantSelected;

    return `/admin-store/deposit/detail/${transferDate}?area=${area}&subArea=${subArea}&merchant=${merchant}`;
  };

  return (
    <div className="space-y-2">
      {/* Summary Table */}
      <Card className="border-0 shadow-none text-[#6F6F6E] rounded-none">
        <CardContent className="pt-6 xl:w-[900px] border-b border-[#6F6F6E] rounded-none">
          <div className="mb-4">
            <span className="text-xl font-medium mr-2">サマリー</span>
            <span className="w-px h-6 bg-[#6F6F6E] inline-block mx-2" />
            <Link
              to={getDepositDetailLink()}
              className="text-[#1D9987] hover:text-[#1D9987]/80 text-xl font-medium ml-2"
            >
              詳細データを確認する
            </Link>
          </div>

          <div className="overflow-x-auto sm:overflow-visible xl:mx-10 text-[#6F6F6E] text-lg">
            <div className='flex flex-col'>
              {/* Header row */}
              <div className='flex flex-row gap-4 xl:gap-8'>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  売上件数
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  売上金額
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  手数料額
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  （内消費税）
                </div>
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  振込金額
                </div>
              </div>
              {/* Data row */}
              <div className='flex flex-row gap-4 xl:gap-8'>
                <div className="py-3 px-0 text-center flex-[1] xl:px-4">
                  {formatNumber(totalPayment?.numberOfSales || 0)}件
                </div>
                <div className="py-3 px-0 text-center flex-[1] xl:px-4">
                  {formatNumber(totalPayment?.salesAmount || 0)}円
                </div>
                <div className="py-3 px-0 text-center flex-[1] xl:px-4">
                  {formatNumber(totalPayment?.totalFee || 0)}円
                </div>
                <div className="py-3 px-0 text-center flex-[1] xl:px-4">
                  ({formatNumber(totalPayment?.sumTax || 0)})円
                </div>
                <div className="py-3 px-0 text-center flex-[1] xl:px-4">
                  {formatNumber(totalPayment?.paymentAmount || 0)}円
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Merchant Payments Table */}
      <Card className='border-0 shadow-none text-[#6F6F6E] rounded-none border-b border-[#6F6F6E]'>
        <CardHeader>
          <CardTitle className="text-xl font-medium">決済種別ごとのデータ</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto text-lg">
            <Table>
              <TableHeader>
                <TableRow className="border-none">
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">加盟店番号</span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">加盟店名</span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">売上件数</span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">売上金額 </span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">手数料額 </span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">（内消費税）</span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">振込額 </span>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.agxMerchantPayments.slice(0, -1).map((item, index) => (
                  <TableRow key={index} className="border-0">
                    <TableCell className="py-3 px-2 text-center bg-white text-lg">
                      {item.merchantNo}
                    </TableCell>
                    <TableCell className="py-3 px-2 text-left bg-white text-lg">
                      {item.storeName}
                    </TableCell>
                    <TableCell className="py-3 px-2 text-center bg-white text-lg">
                      {formatNumber(item.numberOfSales)}件
                    </TableCell>
                    <TableCell className="py-3 px-2 text-center bg-white text-lg">
                      {formatNumber(item.salesAmount)}円
                    </TableCell>
                    <TableCell className="py-3 px-2 text-center bg-white text-lg">
                      {formatNumber(item.totalFee)}円
                    </TableCell>
                    <TableCell className="py-3 px-2 text-center bg-white text-lg">
                      ({formatNumber(item.sumTax)})円
                    </TableCell>
                    <TableCell className="py-3 px-2 text-center bg-white text-lg">
                      {formatNumber(item.paymentAmount)}円
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Deposit Breakdown Table */}
      <Card className='border-0 shadow-none text-[#6F6F6E] rounded-none border-b border-[#6F6F6E]'>
        <CardHeader>
          <CardTitle className="text-xl font-medium">振込内訳</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto text-lg">
            <Table>
              <TableHeader>
                <TableRow className="border-none">
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg w-[25%]">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                      取引区分
                    </span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                      加盟店番号
                    </span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                      売上件数
                    </span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                      売上金額
                    </span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                      手数料率
                    </span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                      手数料額
                    </span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                      （内消費税額）
                    </span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-lg">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                      振込額
                    </span>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(() => {
                  if (!data.agxPaymentBreakdowns) return null;

                  // Transaction type classification helpers
                  const isCredictCardTransaction = (transactionType: number) =>
                    transactionType <= 283260004 || (transactionType >= 283260018 && transactionType <= 283260023);

                  const isElectronicMoneyTransaction = (transactionType: number) =>
                    transactionType === 283260024 || (transactionType >= 283260005 && transactionType <= 283260009);

                  const isQRCodeTransaction = (transactionType: number) =>
                    transactionType >= 283260010 && transactionType <= 283260017;

                  // Group payment breakdowns by category
                  const creditCardItems = data.agxPaymentBreakdowns.filter(item =>
                    isCredictCardTransaction(item.agxTransactionType)
                  );
                  const qrCodeItems = data.agxPaymentBreakdowns.filter(item =>
                    isQRCodeTransaction(item.agxTransactionType)
                  );
                  const electronicMoneyItems = data.agxPaymentBreakdowns.filter(item =>
                    isElectronicMoneyTransaction(item.agxTransactionType)
                  );

                  const renderCategoryGroup = (categoryName: string, items: any[], isBorderTop: boolean = true) => {
                    if (items.length === 0) return null;

                    return (
                      <>
                        {/* Category header row */}
                        <TableRow key={`${categoryName}-header`} className={`${isBorderTop ? 'border-t border-[#6F6F6E]' : 'border-none'} border-b-0`}>
                          <TableCell className="py-3 px-2 text-left text-black font-medium text-lg">
                            {categoryName}
                          </TableCell>
                          <TableCell className="py-3 px-2"></TableCell>
                          <TableCell className="py-3 px-2"></TableCell>
                          <TableCell className="py-3 px-2"></TableCell>
                          <TableCell className="py-3 px-2"></TableCell>
                          <TableCell className="py-3 px-2"></TableCell>
                          <TableCell className="py-3 px-2"></TableCell>
                          <TableCell className="py-3 px-2"></TableCell>
                        </TableRow>
                        {/* Category items */}
                        {items.map((item, index) => (
                          <TableRow
                            key={`${categoryName}-${index}`}
                            className={`border-none ${index % 2 !== 0 ? 'bg-gray-100 hover:bg-gray-100' : 'hover:bg-white'}`}
                          >
                            <TableCell className="py-3 px-2 text-left pl-6 text-lg">
                              <Link
                                to={`/admin-store/deposit/detail/${transferDate}/${item.agxTransactionType}/${item.agxMerchantNo}/${btoa(item.agxPaymentBreakdownId)}`}
                                className="text-[#1D9987] hover:text-[#1D9987]/80"
                              >
                                {mapTransactionType.get(item.agxTransactionType)}{item.groupCodeName}
                              </Link>
                            </TableCell>
                            <TableCell className="py-3 px-2 text-center text-lg">
                              {item.agxMerchantNo}
                            </TableCell>
                            <TableCell className="py-3 px-2 text-center text-lg">
                              {formatNumber(item.agxNumberOfSales)}件
                            </TableCell>
                            <TableCell className="py-3 px-2 text-center text-lg">
                              {formatNumber(item.agxSalesAmount)}円
                            </TableCell>
                            <TableCell className="py-3 px-2 text-center text-lg">
                              {formatNumber(item.agxTotalFeeRate)}%
                            </TableCell>
                            <TableCell className="py-3 px-2 text-center text-lg">
                              {formatNumber(item.agxTotalFee)}円
                            </TableCell>
                            <TableCell className="py-3 px-2 text-center text-lg">
                              ({formatNumber(item.agxInHouseTax)})円
                            </TableCell>
                            <TableCell className="py-3 px-2 text-center text-lg">
                              {formatNumber(item.agxPaymentAmount)}円
                            </TableCell>
                          </TableRow>
                        ))}
                      </>
                    );
                  };

                  return (
                    <>
                      {renderCategoryGroup('クレジットカード', creditCardItems, false)}
                      {renderCategoryGroup('QRコード決済', qrCodeItems)}
                      {renderCategoryGroup('電子マネー', electronicMoneyItems)}
                    </>
                  );
                })()}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Tax Summary Table - Only show for dates after switchLayoutDate */}
      {transferDate > switchLayoutDate && (
        <div className="flex justify-end">
          <Card className="w-96 border-0 shadow-none text-[#6F6F6E] rounded-none">
            <CardContent className="pt-6">
              <Table>
                <TableBody>
                  <TableRow className='border-b border-[#6F6F6E]'>
                    <TableCell className='text-lg'>非課税小計</TableCell>
                    <TableCell className="text-right text-lg">
                      {formatNumber(data.subTotalNonTax)}円
                    </TableCell>
                  </TableRow>
                  <TableRow className='border-b border-[#6F6F6E]'>
                    <TableCell className='text-lg'>10%小計（税込）</TableCell>
                    <TableCell className="text-right text-lg">
                      {formatNumber(data.subTotalInclTax10)}円
                    </TableCell>
                  </TableRow>
                  <TableRow className='border-b border-[#6F6F6E]'>
                    <TableCell className='text-lg'>内消費税額</TableCell>
                    <TableCell className="text-right text-lg">
                      {formatNumber(data.subTotalConsumptionTax)}円
                    </TableCell>
                  </TableRow>
                  <TableRow className='border-b border-[#6F6F6E]'>
                    <TableCell className="text-lg">合計（税込）</TableCell>
                    <TableCell className="text-right text-lg">
                      {formatNumber(data.subTotalTaxIncl)}円
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
